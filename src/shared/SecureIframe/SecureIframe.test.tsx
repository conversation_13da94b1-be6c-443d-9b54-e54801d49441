import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SecureIframe from './SecureIframe';

describe('SecureIframe', () => {
  const defaultProps = {
    src: 'https://example.com',
    title: 'Test Iframe'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders iframe with correct attributes', () => {
    render(<SecureIframe {...defaultProps} />);
    
    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://example.com');
    expect(iframe).toHaveAttribute('title', 'Test Iframe');
    expect(iframe).toHaveAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups');
    expect(iframe).toHaveAttribute('allowfullscreen');
  });

  it('shows loading indicator by default', () => {
    render(<SecureIframe {...defaultProps} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    expect(document.querySelector('.secure-iframe-loading')).toBeInTheDocument();
  });

  it('hides loading indicator when iframe loads', async () => {
    render(<SecureIframe {...defaultProps} />);
    
    const iframe = screen.getByTitle('Test Iframe');
    fireEvent.load(iframe);
    
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
  });



  it('calls onLoad callback when iframe loads', async () => {
    const onLoad = jest.fn();
    render(<SecureIframe {...defaultProps} onLoad={onLoad} />);
    
    const iframe = screen.getByTitle('Test Iframe');
    fireEvent.load(iframe);
    
    await waitFor(() => {
      expect(onLoad).toHaveBeenCalledTimes(1);
    });
  });



  it('applies custom className and styles', () => {
    const customStyle = { backgroundColor: 'red' };
    render(
      <SecureIframe
        {...defaultProps}
        className="custom-class"
        style={customStyle}
      />
    );

    const container = document.querySelector('.secure-iframe-container');
    expect(container).toHaveClass('secure-iframe-container', 'custom-class');

    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toHaveStyle('background-color: red');
  });

  it('supports custom sandbox permissions', () => {
    render(
      <SecureIframe 
        {...defaultProps} 
        sandbox="allow-scripts allow-same-origin"
      />
    );
    
    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toHaveAttribute('sandbox', 'allow-scripts allow-same-origin');
  });

  it('supports allowFullScreen attribute', () => {
    render(<SecureIframe {...defaultProps} />);
    
    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toHaveAttribute('allowfullscreen');
  });

  it('can disable loading indicator', () => {
    render(
      <SecureIframe
        {...defaultProps}
        showLoading={false}
      />
    );

    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });

  it('forwards ref to iframe element', () => {
    const ref = React.createRef<HTMLIFrameElement>();
    render(<SecureIframe {...defaultProps} ref={ref} />);

    expect(ref.current).toBeInstanceOf(HTMLIFrameElement);
    expect(ref.current).toHaveAttribute('src', 'https://example.com');
  });

  it('applies additional iframe attributes via rest props', () => {
    render(
      <SecureIframe
        {...defaultProps}
        width="800"
        height="600"
        data-testid="custom-iframe"
      />
    );

    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toHaveAttribute('width', '800');
    expect(iframe).toHaveAttribute('height', '600');
    expect(iframe).toHaveAttribute('data-testid', 'custom-iframe');
  });

  it('has correct display name', () => {
    expect(SecureIframe.displayName).toBe('SecureIframe');
  });

  it('applies default sandbox permissions when none provided', () => {
    render(<SecureIframe {...defaultProps} />);

    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toHaveAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups');
  });

  it('shows loading state initially', () => {
    render(<SecureIframe {...defaultProps} />);

    const iframe = screen.getByTitle('Test Iframe');
    expect(iframe).toHaveClass('secure-iframe', 'loading');
  });

  it('updates iframe class when loaded', async () => {
    render(<SecureIframe {...defaultProps} />);

    const iframe = screen.getByTitle('Test Iframe');
    fireEvent.load(iframe);

    await waitFor(() => {
      expect(iframe).toHaveClass('secure-iframe', 'loaded');
    });
  });


});
