import React, {
  useState, forwardRef,
} from 'react';
import './SecureIframe.scss';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';

export interface SecureIframeProps extends React.IframeHTMLAttributes<HTMLIFrameElement> {
  src: string;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  showLoading?: boolean;
  showError?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  sandbox?: string;
}

const SecureIframe = forwardRef<HTMLIFrameElement, SecureIframeProps>(({
  src,
  title,
  className = '',
  style = {},
  showLoading = true,
  showError = true,
  onLoad,
  onError,
  sandbox = DEFAULT_SANDBOX,
  ...rest
}, ref) => {
  const [status, setStatus] = useState<'loading' | 'loaded' | 'error'>('loading');

  const handleLoad = () => {
    setStatus('loaded');
    onLoad?.();
  };

  const handleError = () => {
    setStatus('error');
    onError?.();
  };

  return (
    <div className={`secure-iframe-container ${className}`}>
      {status === 'loading' && showLoading && (
        <div className="secure-iframe-loading">
          <div className="loading-spinner" />
          <span>Loading...</span>
        </div>
      )}

      {status === 'error' && showError && (
        <div className="secure-iframe-error">
          <span>⚠️ Failed to load content.</span>
        </div>
      )}

      <iframe
        ref={ref}
        src={src}
        title={title}
        className={`secure-iframe ${status}`}
        style={{
          ...style,
        }}
        sandbox={sandbox}
        allowFullScreen
        onLoad={handleLoad}
        onError={handleError}
        {...rest}
      />
    </div>
  );
});

SecureIframe.displayName = 'SecureIframe';

export default SecureIframe;
